import 'package:flutter/material.dart';
import 'package:get/get.dart';

typedef WidgetCallback<T> = Widget Function(T data);

class EntityStateWidget<T> extends StatefulWidget {
  final Rx<ViewModel<T>> model;
  final WidgetCallback<T>? itemBuilder;

  const EntityStateWidget({super.key, required this.model, this.itemBuilder});

  @override
  State<EntityStateWidget> createState() => _EntityStateWidgetState();
}

class _EntityStateWidgetState extends State<EntityStateWidget> {
  @override
  Widget build(BuildContext context) {
    return Obx(() {
      return switch (widget.model.value.state) {
        // TODO: Handle this case.
        ViewState.loading => throw UnimplementedError(),
        // TODO: Handle this case.
        ViewState.content => throw UnimplementedError(),
        // TODO: Handle this case.
        ViewState.empty => throw UnimplementedError(),
        // TODO: Handle this case.
        ViewState.error => throw UnimplementedError(),
      };
    });
  }
}

enum ViewState { loading, content, empty, error }

class ViewModel<T> {
  final ViewState state;
  final T? data;
  final String? errorMessage;

  const ViewModel.loading()
      : state = ViewState.loading,
        data = null,
        errorMessage = null;

  const ViewModel.content(this.data)
      : state = ViewState.content,
        errorMessage = null;

  const ViewModel.empty()
      : state = ViewState.empty,
        data = null,
        errorMessage = null;

  const ViewModel.error(this.errorMessage)
      : state = ViewState.error,
        data = null;
}

class PaginatedViewModel<T> extends ViewModel<List<T>> {
  final int currentPage;
  final bool hasMore;

  const PaginatedViewModel.loading()
      : currentPage = 1,
        hasMore = false,
        super.loading();

  const PaginatedViewModel.content(
    List<T> super.data, {
    this.currentPage = 1,
    this.hasMore = false,
  }) : super.content();

  const PaginatedViewModel.error(String super.message)
      : currentPage = 1,
        hasMore = false,
        super.error();

  const PaginatedViewModel.empty()
      : currentPage = 1,
        hasMore = false,
        super.empty();
}
