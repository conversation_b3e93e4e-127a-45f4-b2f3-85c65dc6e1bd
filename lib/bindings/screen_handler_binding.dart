import 'package:darve/controllers/profile_controller.dart';
import 'package:darve/controllers/chat_list_controller.dart';
import 'package:darve/providers/auth_provider.dart';
import 'package:get/get.dart';

class ScreenHandlerBinding extends Bindings {
  @override
  void dependencies() {
    // Register controllers needed for ScreenHandler
    Get.lazyPut<ChatListController>(() => ChatListController());

    // Get current user data from auth
    final user = AuthProvider.auth.user;

    if (user != null) {
      // Register ProfileController for current user
      Get.lazyPut<ProfileController>(
        () => ProfileController(
          userId: user.id,
          userName: user.username,
        ),
        tag: user.id, // Use user ID as tag
      );
    }
  }
}
