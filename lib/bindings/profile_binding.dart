import 'package:darve/controllers/profile_controller.dart';
import 'package:darve/controllers/chat_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ProfileBinding extends Bindings {
  @override
  void dependencies() {
    // Get route arguments
    final args = Get.arguments as Map<String, String>;
    final userId = args['userId']!;
    final username = args['username']!;

    // Register ProfileController with proper parameters
    Get.lazyPut<ProfileController>(
      () => ProfileController(
        userId: userId,
        userName: username,
      ),
      tag: userId, // Use userId as tag for multiple profile instances
    );
  }
}
