import 'package:darve/controllers/profile_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Helper class for managing ProfileController instances dynamically
class ProfileControllerHelper {
  /// Register ProfileController for a specific user if not already registered
  static void registerProfileController({
    required String userId,
    required String username,
  }) {
    if (!Get.isRegistered<ProfileController>(tag: userId)) {
      Get.lazyPut<ProfileController>(
        () => ProfileController(
          userId: userId,
          userName: username,
        ),
        tag: userId,
      );
    }
  }

  /// Get ProfileController for a specific user
  static ProfileController getProfileController(String userId) {
    return Get.find<ProfileController>(tag: userId);
  }

  /// Check if ProfileController is registered for a user
  static bool isProfileControllerRegistered(String userId) {
    return Get.isRegistered<ProfileController>(tag: userId);
  }

  /// Dispose ProfileController for a specific user
  static void disposeProfileController(String userId) {
    if (Get.isRegistered<ProfileController>(tag: userId)) {
      Get.delete<ProfileController>(tag: userId);
    }
  }

  /// Register ProfileController and navigate to ProfilePage
  static void navigateToProfile({
    required String userId,
    required String username,
    required String imageUrl,
    required String fullName,
    required Function(Widget) navigator,
  }) {
    // Register controller
    registerProfileController(userId: userId, username: username);
    
    // Import ProfilePage here to avoid circular dependencies
    // This will be handled in the calling component
  }
}
