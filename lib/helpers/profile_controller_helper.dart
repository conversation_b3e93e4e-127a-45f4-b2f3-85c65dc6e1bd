import 'package:darve/controllers/profile_controller.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// TODO @sj: added for backward compatibility with legacy code
/// Need to replace with binding initialisation
/// Helper class for managing ProfileController instances dynamically
class ProfileControllerHelper {
  /// Register ProfileController for a specific user if not already registered
  static void registerProfileController({
    required String userId,
    required String username,
  }) {
    if (!Get.isRegistered<ProfileController>(tag: userId)) {
      Get.lazyPut<ProfileController>(
        () => ProfileController(
          userId: userId,
          userName: username,
        ),
        tag: userId,
      );
    }
  }
}
