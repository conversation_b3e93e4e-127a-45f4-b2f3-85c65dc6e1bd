import 'package:darve/api/models/user_model.dart';
import 'package:darve/providers/auth_provider.dart';
import 'package:darve/routes/route_helper.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/services/error/error_models.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';


class AuthController extends GetxController {
  RxBool isPasswordVisible = false.obs;
  RxBool isContainerVisible = false.obs;

  late final AuthService _authService;

  // Expose auth service state through controller properties
  RxBool isLoggedIn = false.obs;
  UserModel? user;

  final username = TextEditingController();
  final password = TextEditingController();

  // Form key for validation
  final formKey = GlobalKey<FormState>();

  // Loading state for sign in
  RxBool isSigningIn = false.obs;

  @override
  void onInit() {
    super.onInit();
    _initializeAuthService();

    Future.delayed(const Duration(milliseconds: 100), () {
      isContainerVisible.value = true;
    });
  }

  Future<void> _initializeAuthService() async {
    _authService = AuthProvider.auth;
    _setupAuthServiceListeners();
  }

  // Setup listeners to sync auth service state with controller state
  void _setupAuthServiceListeners() {
    ever(_authService.authState, (authState) {
      isLoggedIn.value = authState.isLoggedIn;
      user = authState.user;
    });
  }

  // Authentication methods - delegate to auth service
  void signIn() async {
    if (!formKey.currentState!.validate()) {
      return; // Don't proceed if validation fails
    }

    try {
      isSigningIn.value = true;
      await _authService.signInWithEmailPassword(username.text.trim(), password.text);
    } finally {
      isSigningIn.value = false;
    }
  }

  void signInWithApple() async {
    await _authService.signInWithApple();
  }

  void signInWithGoogle() async {
    await _authService.signInWithGoogle();
  }

  void signInWithFacebook() async {
    await _authService.signInWithFacebook();
  }

  // Error handling - expose auth service errors
  AuthError? get error => _authService.error;
  
  void clearError() {
    _authService.clearError();
  }

  // Loading state - expose auth service loading state
  bool get isLoading => _authService.loadingState == AuthLoadingState.loading;

  // Logout method
  Future<void> logout() async {
    await _authService.logout();
    // Navigate to login page after logout
    RouteHelper.goToLogin();
  }
}
