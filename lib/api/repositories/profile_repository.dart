import 'package:darve/utils/interfaces/Follower.dart';
import 'package:darve/utils/interfaces/ProfileData.dart';
import 'package:darve/services/http/http_service.dart';
import 'package:darve/utils/constants.dart';

class ProfileRepository {
  final HttpService _dioService;

  ProfileRepository(this._dioService);

  Future<ProfileData> getProfileData(String username) async {
    if (username.startsWith("@")) {
      username = username.split("@")[1];
    }

    final response = await _dioService.get(ApiPaths.userProfile(username));

    if (response.data != null) {
      return ProfileData.fromJson(response.data);
    }
    return ProfileData.empty();
  }

  Future<dynamic> followUser(String userId) async {
    final response = await _dioService.post(ApiPaths.followUser(userId));
    return response.data;
  }

  Future<dynamic> unfollowUser(String userId) async {
    final response = await _dioService.post(ApiPaths.unfollowUser(userId));
    return response.data;
  }

  Future<dynamic> searchUser(String userInput) async {
    final response = await _dioService.post(
      ApiPaths.searchUser,
      data: {'query': userInput},
    );

    return response.data['items'] ?? [];
  }

  Future<dynamic> getFollowers(String userId) async {
    final response = await _dioService.get(ApiPaths.getFollowers(userId));
    // Assuming responseData['list'] is a List of dynamic objects.
    List<Follower> followers = (response.data['items'] as List)
        .map((item) => Follower(
              username: item['username'],
              name: item['name'],
              imageUrl: item['image_url'],
            ))
        .toList();
    return followers;
  }

  Future<bool> isFollowing(String userId) async {
    final response = await _dioService.get(ApiPaths.isFollowing(userId));
    return response.data['is_following'] ?? false;
  }

  Future<dynamic> editProfile(String profileData, String imagePath) async {
    final response = await _dioService.post(
      ApiPaths.editProfile,
      data: profileData,
    );
    return response.data;
  }

  Future<dynamic> getFollowing(String userId) async {
    final response = await _dioService.get(ApiPaths.getFollowing(userId));
    return response.data;
  }
}
