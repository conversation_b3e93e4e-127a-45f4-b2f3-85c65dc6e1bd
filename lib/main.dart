import 'package:darve/config.dart';
import 'package:darve/ui/auth/sigin/sign_in_controller.dart';
import 'package:darve/controllers/chat_list_controller.dart';
import 'package:darve/providers/auth_provider.dart';
import 'package:darve/routes/app_pages.dart';
import 'package:darve/routes/app_routes.dart';
import 'package:darve/services/error/error_reporting_service.dart';
import 'package:darve/utils/request_cache.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:get/get.dart';
import 'package:flutter_stripe/flutter_stripe.dart';

final GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

void main() async {
  await dotenv.load();
  AppConfig.fromEnv();
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize error reporting service
  // TODO: Add your Sentry DSN from environment variables
  final sentryDsn = dotenv.env['SENTRY_DSN'];
  if (sentryDsn != null && sentryDsn.isNotEmpty) {
    await ErrorReportingService.initialize(
      dsn: sentryDsn,
      environment: AppConfig.instance.environment,
      sampleRate: AppConfig.instance.isProduction ? 0.1 : 1.0,
    );
  } else {
    debugPrint('⚠️ Sentry DSN not found in environment variables. Error reporting disabled.');
  }

  //Stripe init
  Stripe.publishableKey = AppConfig.instance.stripePublishableKey;
  Stripe.merchantIdentifier = 'merchant.flutter.stripe.test';
  Stripe.urlScheme = 'darvemobile';
  await Stripe.instance.applySettings();

  // Initialize new auth service
  await AuthProvider.initialize();

  // Initialize controllers that depend on auth service
  Get.put(ChatListController());
  Get.put(SignInController(), permanent: true);

  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    RequestCache.getInstance();

    // Determine initial route based on auth state
    final authService = AuthProvider.auth;
    final initialRoute = authService.isLoggedIn ? AppRoutes.home : AppRoutes.login;

    return GetMaterialApp(
      navigatorKey: navigatorKey,
      title: 'Darve',
      debugShowCheckedModeBanner: false,
      initialRoute: initialRoute,
      getPages: AppPages.pages,
      theme: ThemeData(
        fontFamily: 'Rubik',
        primaryColor: Styles.primaryColor, // Main color
        colorScheme: ColorScheme.fromSeed(
          seedColor: Styles.primaryColor, // Main color for Material 3
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: Styles.primaryColor, // Button color
            foregroundColor: Colors.white, // Text color
            textStyle: const TextStyle(
              fontWeight: FontWeight.w600,
              fontSize: 18,
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 14),
            elevation: 0,
          ),
        ),
      ),
    );
  }
}
