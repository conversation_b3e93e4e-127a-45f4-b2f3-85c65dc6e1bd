import 'package:darve/bindings/profile_binding.dart';
import 'package:darve/pages/profile_page.dart';
import 'package:flutter/material.dart';

/// Wrapper for ProfilePage that handles binding initialization for direct usage
/// Use this when navigating to ProfilePage directly (not through routes)
class ProfilePageWrapper extends StatefulWidget {
  final String userId;
  final String username;
  final String imageUrl;
  final String fullName;
  final VoidCallback? goBackToReels;

  const ProfilePageWrapper({
    super.key,
    required this.userId,
    required this.username,
    required this.imageUrl,
    required this.fullName,
    this.goBackToReels,
  });

  @override
  State<ProfilePageWrapper> createState() => _ProfilePageWrapperState();
}

class _ProfilePageWrapperState extends State<ProfilePageWrapper> {
  @override
  void initState() {
    super.initState();
    // Initialize binding for direct usage
    DirectProfileBinding.initialize(
      userId: widget.userId,
      username: widget.username,
    );
  }

  @override
  void dispose() {
    // Clean up binding when widget is disposed
    DirectProfileBinding.dispose(widget.userId);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ProfilePage(
      widget.userId,
      widget.username,
      widget.imageUrl,
      widget.fullName,
      goBackToReels: widget.goBackToReels,
    );
  }
}
