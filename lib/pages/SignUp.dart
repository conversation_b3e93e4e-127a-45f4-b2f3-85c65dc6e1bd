import 'package:darve/components/common/ProfilePictureUploader.dart';
import 'package:darve/controllers/register_controller.dart';
import 'package:darve/routes/route_helper.dart';
import 'package:darve/utils/styles.dart';
import 'package:darve/utils/validators.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SignUpPage extends GetView<RegisterController> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            color: Styles.primaryColor,
            height: double.infinity,
            width: double.infinity,
          ),
          Align(
            alignment: Alignment.topCenter,
            child: Padding(
              padding: const EdgeInsets.only(top: 20),
              child: SizedBox(
                width: 220,
                child: Image.asset(
                  "assets/images/darve-no-bg.png",
                  fit: BoxFit.fitWidth,
                ),
              ),
            ),
          ),
          Positioned(
            top: 50.0,
            left: 8.0,
            child: IconButton(
              icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
              onPressed: () {
                RouteHelper.goBack();
              },
            ),
          ),
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              decoration: const BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(50.0),
                  topRight: Radius.circular(50.0),
                ),
                color: Styles.foregroundColor,
              ),
              height: MediaQuery.of(context).size.height * 0.75,
              width: double.infinity,
              child: SingleChildScrollView(
                child: Obx(() {
                  controller.errorsArray.isEmpty;
                  return controller.isFirstStage.value
                      ? Column(
                          children: [
                            const Padding(
                              padding: EdgeInsets.only(top: 32.0, bottom: 10.0),
                              child: Text(
                                "Sign up",
                                style: TextStyle(
                                  fontSize: 22.0,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            const Text(
                              "Create an account to continue",
                              style: TextStyle(
                                color: Styles.textLightColor,
                                fontWeight: FontWeight.w400,
                                fontSize: 14,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(
                                  top: 2.0, left: 24.0, right: 24.0),
                              child: Form(
                                key: controller.firstStageFormKey,
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                  const Padding(
                                    padding:
                                        EdgeInsets.only(left: 4, bottom: 4.0),
                                    child: Text(
                                      "Email",
                                      style: TextStyle(
                                        color: Styles.textLightColor,
                                        fontSize: 14.0,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ),
                                  TextFormField(
                                    controller: controller.emailController,
                                    validator: Validators.validateEmail,
                                    decoration: InputDecoration(
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(16.0),
                                        borderSide: const BorderSide(
                                          color: Styles
                                              .primaryColor, // Your desired color
                                          width: 1.0,
                                        ),
                                      ),
                                      filled: true,
                                      hintText: "<EMAIL>",
                                      hintStyle: const TextStyle(
                                          color: Styles.textLightColor,
                                          fontSize: 15.0),
                                      fillColor: Colors.white,
                                      border: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(16.0),
                                        borderSide: BorderSide.none,
                                      ),
                                      prefixIcon: SizedBox(
                                        width: 16,
                                        height: 16,
                                        child: Center(
                                          child: Image.asset(
                                            'assets/images/sign_in/envelope.png',
                                            color: Styles.primaryColor,
                                            fit: BoxFit.contain,
                                            width: 16,
                                            height: 12,
                                          ),
                                        ),
                                      ),
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                              vertical: 15.0, horizontal: 20.0),
                                    ),
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const Padding(
                                    padding: EdgeInsets.only(
                                        left: 4, top: 4, bottom: 4.0),
                                    child: Text(
                                      "Full Name",
                                      style: TextStyle(
                                        color: Styles.textLightColor,
                                        fontSize: 14.0,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ),
                                  TextFormField(
                                    controller: controller.fullNameController,
                                    validator: Validators.validateFullName,
                                    decoration: InputDecoration(
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(16.0),
                                        borderSide: const BorderSide(
                                          color: Styles
                                              .primaryColor, // Your desired color
                                          width: 1.0,
                                        ),
                                      ),
                                      filled: true,
                                      hintText: "Your Name",
                                      hintStyle: const TextStyle(
                                          color: Styles.textLightColor,
                                          fontSize: 15.0),
                                      fillColor: Colors.white,
                                      border: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(16.0),
                                        borderSide: BorderSide.none,
                                      ),
                                      prefixIcon: SizedBox(
                                        width: 17,
                                        height: 17,
                                        child: Center(
                                          child: Image.asset(
                                            'assets/images/sign_in/mage_user.png',
                                            color: Styles.primaryColor,
                                            fit: BoxFit.contain,
                                            width: 17,
                                            height: 17,
                                          ),
                                        ),
                                      ),
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                              vertical: 15.0, horizontal: 20.0),
                                    ),
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const Padding(
                                    padding: EdgeInsets.only(
                                        left: 4, top: 4, bottom: 4.0),
                                    child: Text(
                                      "Password",
                                      style: TextStyle(
                                        color: Styles.textLightColor,
                                        fontSize: 14.0,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ),
                                  Obx(() {
                                    return TextFormField(
                                      controller: controller.password1Controller,
                                      validator: Validators.validatePassword,
                                      obscureText:
                                          !controller.isPasswordVisible.value,
                                      obscuringCharacter: '*',
                                      decoration: InputDecoration(
                                        focusedBorder: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(16.0),
                                          borderSide: const BorderSide(
                                            color: Styles
                                                .primaryColor, // Your desired color
                                            width: 1.0,
                                          ),
                                        ),
                                        filled: true,
                                        hintText: "********",
                                        hintStyle: const TextStyle(
                                            color: Styles.textLightColor,
                                            fontSize: 16.0),
                                        fillColor: Colors.white,
                                        border: OutlineInputBorder(
                                          borderRadius:
                                              BorderRadius.circular(16.0),
                                          borderSide: BorderSide.none,
                                        ),
                                        prefixIcon: SizedBox(
                                          width: 18,
                                          height: 16,
                                          child: Center(
                                            child: Image.asset(
                                              'assets/images/sign_in/lock.png',
                                              color: Styles.primaryColor,
                                              fit: BoxFit.contain,
                                              width: 16,
                                              height: 18,
                                            ),
                                          ),
                                        ),
                                        suffixIcon: IconButton(
                                          icon: Icon(
                                            controller.isPasswordVisible.value
                                                ? Icons.visibility_outlined
                                                : Icons.visibility_off_outlined,
                                            color: Styles.primaryColor,
                                            size: 16,
                                          ),
                                          onPressed: () {
                                            controller.isPasswordVisible.value =
                                                !controller
                                                    .isPasswordVisible.value;
                                          },
                                        ),
                                        contentPadding:
                                            const EdgeInsets.symmetric(
                                                vertical: 15.0,
                                                horizontal: 20.0),
                                      ),
                                      style: const TextStyle(
                                        fontWeight: FontWeight.w600,
                                      ),
                                    );
                                  }),
                                  const Padding(
                                    padding: EdgeInsets.only(
                                        left: 4, top: 4, bottom: 4.0),
                                    child: Text(
                                      "Re-enter Password",
                                      style: TextStyle(
                                        color: Styles.textLightColor,
                                        fontSize: 14.0,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ),
                                  TextFormField(
                                    controller: controller.password2Controller,
                                    validator: controller.validateConfirmPassword,
                                    obscureText:
                                        !controller.isPasswordVisible.value,
                                    obscuringCharacter: '*',
                                    decoration: InputDecoration(
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(16.0),
                                        borderSide: const BorderSide(
                                          color: Styles
                                              .primaryColor, // Your desired color
                                          width: 1.0,
                                        ),
                                      ),
                                      filled: true,
                                      hintText: "********",
                                      hintStyle: const TextStyle(
                                          color: Styles.textLightColor,
                                          fontSize: 16.0),
                                      fillColor: Colors.white,
                                      border: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(16.0),
                                        borderSide: BorderSide.none,
                                      ),
                                      prefixIcon: SizedBox(
                                        width: 18,
                                        height: 16,
                                        child: Center(
                                          child: Image.asset(
                                            'assets/images/sign_in/lock.png',
                                            color: Styles.primaryColor,
                                            fit: BoxFit.contain,
                                            width: 16,
                                            height: 18,
                                          ),
                                        ),
                                      ),
                                      suffixIcon: IconButton(
                                        icon: Icon(
                                          controller.isPasswordVisible.value
                                              ? Icons.visibility_outlined
                                              : Icons.visibility_off_outlined,
                                          color: Styles.primaryColor,
                                          size: 16,
                                        ),
                                        onPressed: () {
                                          controller.isPasswordVisible.value =
                                              !controller
                                                  .isPasswordVisible.value;
                                        },
                                      ),
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                              vertical: 15.0, horizontal: 20.0),
                                    ),
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                                  const Padding(
                                    padding: EdgeInsets.only(
                                        left: 4, top: 20, bottom: 4.0),
                                    child: Text(
                                      "Date of Birth",
                                      style: TextStyle(
                                        color: Styles.textLightColor,
                                        fontSize: 14.0,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                        horizontal: 20),
                                    child: Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.center,
                                      children: [
                                        Expanded(
                                          flex: 3,
                                          child: Obx(() {
                                            return DropdownButtonFormField<
                                                String>(
                                              decoration: InputDecoration(
                                                filled: true,
                                                fillColor: Colors.white,
                                                border: OutlineInputBorder(
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          10.0),
                                                  borderSide: BorderSide.none,
                                                ),
                                              ),
                                              hint: const Text(
                                                'Day',
                                                style: TextStyle(
                                                  color: Colors.black,
                                                  fontSize: 14.0,
                                                  fontWeight: FontWeight.w400,
                                                ),
                                              ),
                                              value:
                                                  controller.selectedDay.value,
                                              onChanged: (String? newValue) {
                                                controller.selectedDay.value =
                                                    newValue ?? '';
                                              },
                                              items: controller.days.map<
                                                      DropdownMenuItem<String>>(
                                                  (String day) {
                                                return DropdownMenuItem<String>(
                                                  value: day,
                                                  child: Text(day),
                                                );
                                              }).toList(),
                                            );
                                          }),
                                        ),
                                        const SizedBox(width: 6.0),
                                        Expanded(
                                          flex: 3,
                                          child:
                                              DropdownButtonFormField<String>(
                                            decoration: InputDecoration(
                                              filled: true,
                                              fillColor: Colors.white,
                                              border: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(10.0),
                                                borderSide: BorderSide.none,
                                              ),
                                            ),
                                            hint: const Text(
                                              'Month',
                                              style: TextStyle(
                                                color: Colors.black,
                                                fontSize: 14.0,
                                                fontWeight: FontWeight.w400,
                                              ),
                                            ),
                                            value:
                                                controller.selectedMonth.value,
                                            onChanged: (String? newValue) {
                                              controller.selectedMonth.value =
                                                  newValue ?? '';
                                            },
                                            items: controller.months
                                                .map<DropdownMenuItem<String>>(
                                                    (String month) {
                                              return DropdownMenuItem<String>(
                                                value: month,
                                                child: Text(month),
                                              );
                                            }).toList(),
                                          ),
                                        ),
                                        const SizedBox(width: 6.0),
                                        Expanded(
                                          flex: 3,
                                          child:
                                              DropdownButtonFormField<String>(
                                            decoration: InputDecoration(
                                              filled: true,
                                              fillColor: Colors.white,
                                              border: OutlineInputBorder(
                                                borderRadius:
                                                    BorderRadius.circular(10.0),
                                                borderSide: BorderSide.none,
                                              ),
                                            ),
                                            hint: const Text(
                                              'Year',
                                              style: TextStyle(
                                                color: Colors.black,
                                                fontSize: 14.0,
                                                fontWeight: FontWeight.w400,
                                              ),
                                            ),
                                            value:
                                                controller.selectedYear.value,
                                            onChanged: (String? newValue) {
                                              controller.selectedYear.value =
                                                  newValue ?? '';
                                            },
                                            items: controller.years
                                                .map<DropdownMenuItem<String>>(
                                                    (String year) {
                                              return DropdownMenuItem<String>(
                                                value: year,
                                                child: Text(year),
                                              );
                                            }).toList(),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(height: 60.0),
                                  Center(
                                    child: SizedBox(
                                      width: MediaQuery.of(context).size.width *
                                          0.675,
                                      child: Obx(() => ElevatedButton(
                                        onPressed: controller.isRegistering.value
                                            ? null
                                            : () async {
                                                controller.proceedToNextStage();
                                              },
                                        child: controller.isRegistering.value
                                            ? const SizedBox(
                                                height: 20,
                                                width: 20,
                                                child: CircularProgressIndicator(
                                                  strokeWidth: 2,
                                                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                                ),
                                              )
                                            : const Text("Create an account"),
                                      )),
                                    ),
                                  ),
                                  const SizedBox(height: 18.0),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        )
                      : Column(
                          children: [
                            const Padding(
                              padding: EdgeInsets.only(top: 32.0, bottom: 2.0),
                              child: Text(
                                "Create a username",
                                style: TextStyle(
                                  fontSize: 22.0,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            const Text(
                              "To continue...",
                              style: TextStyle(
                                  color: Styles.textLightColor,
                                  fontWeight: FontWeight.w400,
                                  fontSize: 14),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(
                                  top: 26.0, left: 24.0, right: 24.0),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Center(
                                    child: Column(
                                      children: [
                                        ProfilePictureUploader(
                                          voidCallback: (val) {
                                            controller.filepath = val;
                                          },
                                        ),
                                        const SizedBox(height: 8.0),
                                        const Text("Select a profile picture",
                                            style: TextStyle(
                                                color: Styles.textLightColor,
                                                fontWeight: FontWeight.w400,
                                                fontSize: 14)),
                                      ],
                                    ),
                                  ),
                                  const SizedBox(height: 16.0),
                                  TextFormField(
                                    controller: controller.usernameController,
                                    validator: Validators.validateUsername,
                                    decoration: InputDecoration(
                                      focusedBorder: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(16.0),
                                        borderSide: const BorderSide(
                                          color: Styles
                                              .primaryColor, // Your desired color
                                          width: 1.0,
                                        ),
                                      ),
                                      filled: true,
                                      hintText: "username",
                                      hintStyle: const TextStyle(
                                          color: Styles.textLightColor,
                                          fontSize: 15.0),
                                      fillColor: Colors.white,
                                      border: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(16.0),
                                        borderSide: BorderSide.none,
                                      ),
                                      prefixIcon: SizedBox(
                                        width: 17,
                                        height: 17,
                                        child: Center(
                                          child: Image.asset(
                                            'assets/images/sign_in/mage_user.png',
                                            color: Styles.primaryColor,
                                            fit: BoxFit.contain,
                                            width: 17,
                                            height: 17,
                                          ),
                                        ),
                                      ),
                                      contentPadding:
                                          const EdgeInsets.symmetric(
                                              vertical: 15.0, horizontal: 20.0),
                                    ),
                                    style: const TextStyle(
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const SizedBox(height: 16.0),
                                  Wrap(
                                    crossAxisAlignment:
                                        WrapCrossAlignment.center,
                                    children: [
                                      Obx(() {
                                        return Checkbox(
                                            activeColor: Styles.primaryColor,
                                            value: controller
                                                .isCheckBoxClicked.value,
                                            onChanged: (val) {
                                              controller.isCheckBoxClicked
                                                  .value = val!;
                                            });
                                      }),
                                      const Text(
                                        "I agree to ",
                                        style: TextStyle(
                                            fontSize: 12,
                                            fontWeight: FontWeight.w400),
                                      ),
                                      const Text(
                                        "terms and conditions",
                                        style: TextStyle(
                                            decoration:
                                                TextDecoration.underline,
                                            fontSize: 12,
                                            fontWeight: FontWeight.w400),
                                      ),
                                      const Text(" of Darve application.",
                                          style: TextStyle(
                                              fontSize: 12,
                                              fontWeight: FontWeight.w400)),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: 32.0,
                                  ),
                                  Obx(() {
                                    return !controller.isCheckBoxClicked.value
                                        ? const SizedBox()
                                        : Center(
                                            child: SizedBox(
                                              width: MediaQuery.of(context)
                                                      .size
                                                      .width *
                                                  0.675,
                                              child: Obx(() => ElevatedButton(
                                                onPressed: controller.isRegistering.value
                                                    ? null
                                                    : () async {
                                                        controller.register();
                                                      },
                                                style: ElevatedButton.styleFrom(
                                                  backgroundColor:
                                                      Styles.primaryColor,
                                                  padding: const EdgeInsets
                                                      .symmetric(
                                                      horizontal: 30,
                                                      vertical: 12.0),
                                                  shape: RoundedRectangleBorder(
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            10),
                                                  ),
                                                  elevation: 0,
                                                ),
                                                child: controller.isRegistering.value
                                                    ? const SizedBox(
                                                        height: 20,
                                                        width: 20,
                                                        child: CircularProgressIndicator(
                                                          strokeWidth: 2,
                                                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                                        ),
                                                      )
                                                    : const Text(
                                                        "Log In",
                                                        style: TextStyle(
                                                            color: Colors.white,
                                                            fontSize: 18.0,
                                                            fontWeight:
                                                                FontWeight.bold),
                                                      ),
                                              )),
                                            ),
                                          );
                                  }),
                                  const SizedBox(height: 18.0),
                                ],
                              ),
                            ),
                          ],
                        );
                }),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
