import 'package:darve/components/ProfilePage/ButtonsGroup.dart';
import 'package:darve/components/ProfilePage/CreatePostBtn.dart';
import 'package:darve/components/ProfilePage/SocialGroup.dart';
import 'package:darve/components/ProfilePage/UserAggregate.dart';
import 'package:darve/components/ProfilePage/post_modal.dart';
import 'package:darve/components/common/TopBar.dart';
import 'package:darve/components/common/showSnackbar.dart';
import 'package:darve/controllers/chat_controller.dart';
import 'package:darve/controllers/profile_controller.dart';
import 'package:darve/helpers/user_helper.dart';
import 'package:darve/routes/route_helper.dart';
import 'package:darve/utils/interfaces/Post.dart';
import 'package:darve/utils/medialinks.dart';
import 'package:darve/utils/serverAssets.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../components/PostCard.dart';
import '../components/common/LinearCard.dart';
import 'package:sliding_up_panel2/sliding_up_panel2.dart';

class ProfilePage extends GetView<ProfileController> {
  final String userId;
  final String username;
  final String imageUrl;
  final String fullName;
  final VoidCallback? goBackToReels;

  const ProfilePage(
    this.userId,
    this.username,
    this.imageUrl,
    this.fullName, {
    this.goBackToReels,
    super.key,
  });

  @override
  String? get tag => userId; // Use userId as tag to get the correct controller instance

  // Create scroll controller as a lazy singleton for this profile
  ScrollController get scrollController {
    final tag = 'profile_scroll_$userId';
    if (!Get.isRegistered<ScrollController>(tag: tag)) {
      Get.lazyPut<ScrollController>(() => ScrollController(), tag: tag);
    }
    return Get.find<ScrollController>(tag: tag);
  }

  // Get or create ChatController
  ChatController get chatController {
    if (!Get.isRegistered<ChatController>()) {
      Get.lazyPut<ChatController>(() => ChatController());
    }
    return Get.find<ChatController>();
  }

  void _showPostModal(BuildContext context) {
    PostModal(
      (pickedFilePath, content) async {
        if (content.isEmpty || pickedFilePath.isEmpty) {
          return;
        }
        Navigator.of(context).pop();
        controller.content.value = content;
        await controller.createPost(filePath: pickedFilePath);
        pickedFilePath = "";
      },
      (content) {
        controller.content.value = content;
      },
      '',
    ).showPostModal(context);
  }

  void createChatWithUserId() async {
    final discussionId = await controller.createChatWithUser();
    debugPrint("discussionId===$discussionId");

    if (discussionId != null) {
      RouteHelper.goToChat(
        chatId: discussionId,
        title: username,
        avatarUrl: imageUrl,
        userId: userId,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return SlidingUpPanel(
        maxHeight: MediaQuery.of(context).size.height * 0.80,
        minHeight: MediaQuery.of(context).size.height *
            (controller.isMe.value ? 0.50 : 0.55),
        body: Stack(
          children: [
            if (imageUrl.isNotEmpty)
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Image.network(
                  ServerAssets().getAssetUrl(imageUrl),
                  height: 450,
                  fit: BoxFit.cover,
                ),
              ),
            Obx(() {
              return TopBar(
                  title: "Profile",
                  isTransparent: true,
                  isCurrentUser: controller.isMe.value,
                  callback: goBackToReels);
            })
          ],
        ),
        scrollController: scrollController,
        borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(40.0), topRight: Radius.circular(40.0)),
        panelBuilder: () => SizedBox(
              height: double.infinity,
              child: SingleChildScrollView(
                controller: scrollController,
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Obx(() {
                      if (controller.isLoadingProfile.value) {
                        return const Center(
                          child: Padding(
                            padding: EdgeInsets.all(50.0),
                            child: CircularProgressIndicator(),
                          ),
                        );
                      }

                      return Column(
                        children: [
                          Text(
                            fullName,
                            style: const TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 2),
                          if (controller.userProfile.value.bio!.isNotEmpty)
                            SizedBox(
                              width: MediaQuery.of(context).size.width * 0.8,
                              child: Text(
                                controller.userProfile.value.bio!,
                                style: const TextStyle(
                                  fontSize: 16,
                                  color: Colors.grey,
                                ),
                                textAlign: TextAlign.center,
                              ),
                            ),
                          const SizedBox(height: 16),
                          SocialGroup(
                              controller.userProfile.value.socialLinks!,
                              controller.userProfile.value.platforms),
                          const SizedBox(height: 24),
                          UserAggregate(
                              controller.userProfile.value.followingNr
                                  .toString(),
                              controller.userProfile.value.followersNr
                                  .toString(),
                              controller.posts,
                              userId),
                          if (!controller.isMe.value)
                            const SizedBox(height: 24),
                          if (!controller.isMe.value)
                            Obx(() => ProfileButtonsGroup(
                              userId: userId,
                              username: username,
                              imageUrl: imageUrl,
                              isFollowing: controller.isFollowing.value,
                              isFollowLoading: controller.isFollowLoading.value,
                              isChatCreating: controller.isChatCreating.value,
                              onToggleFollow: () async {
                                await controller.toggleFollow();
                                // Show snackbar feedback
                                SnackbarHelper.showFollowSnackbar(
                                  context: context,
                                  imageUri: imageUrl,
                                  username: username,
                                  bgColor: controller.isFollowing.value
                                      ? Colors.green
                                      : Colors.red,
                                  isFollowed: controller.isFollowing.value,
                                );
                              },
                              onCreateChat: createChatWithUserId,
                            )),
                          const SizedBox(height: 24),
                          // ElevatedButton(
                          //   onPressed: handlePostButtonClick,
                          //   child: Text("Post"),
                          // ),
                          SizedBox(
                            height: 500,
                            child: DefaultTabController(
                              length: 2,
                              child: Builder(
                                builder: (context) {
                                  final TabController tabController =
                                      DefaultTabController.of(context);
                                  // tabController.addListener(() {
                                  //   setState(() {});
                                  // });

                                  return Column(
                                    children: [
                                      TabBar(
                                        indicatorColor: Colors.black,
                                        indicatorWeight: 1,
                                        indicatorSize: TabBarIndicatorSize.tab,
                                        tabs: [
                                          Tab(
                                            icon: Image.asset(
                                                'assets/images/profile/feed.png',
                                                color: Colors.black,
                                                width: 25,
                                                height: 25,
                                                semanticLabel: 'Tab 1'),
                                          ),
                                          Tab(
                                            icon: Image.asset(
                                                'assets/images/profile/people.png',
                                                color: Colors.black,
                                                width: 25,
                                                height: 25,
                                                semanticLabel: 'Tab 2'),
                                          ),
                                        ],
                                      ),
                                      Expanded(
                                        child: TabBarView(
                                          children: [
                                            SingleChildScrollView(
                                              physics:
                                                  const AlwaysScrollableScrollPhysics(),
                                              child: controller.posts.isEmpty
                                                  ? Padding(
                                                      padding:
                                                          const EdgeInsets.all(
                                                              16.0),
                                                      child: Obx(() {
                                                        return controller
                                                                .isLoadingPosts
                                                                .value
                                                            ? const Center(
                                                                child:
                                                                    CircularProgressIndicator())
                                                            : controller
                                                                    .isMe.value
                                                                ? SizedBox(
                                                                    height: 180,
                                                                    width: 120,
                                                                    child: Obx(() => CreatePostBtn(
                                                                        _showPostModal,
                                                                        isPostCreating: controller.isPostCreating.value)))
                                                                : const Center(
                                                                    child: Text(
                                                                        "No Posts yet!",
                                                                        style: Styles
                                                                            .lightTextStyle),
                                                                  );
                                                      }),
                                                    )
                                                  : Column(
                                                      children: [
                                                        GridView.builder(
                                                          physics:
                                                              const NeverScrollableScrollPhysics(),
                                                          shrinkWrap: true,
                                                          padding:
                                                              const EdgeInsets
                                                                  .all(8),
                                                          gridDelegate:
                                                              const SliverGridDelegateWithFixedCrossAxisCount(
                                                            crossAxisCount: 3,
                                                            crossAxisSpacing: 2,
                                                            mainAxisSpacing: 8,
                                                            childAspectRatio:
                                                                0.6,
                                                          ),
                                                          itemCount: controller.posts
                                                                  .length +
                                                              (controller
                                                                      .isMe
                                                                      .value
                                                                  ? 1
                                                                  : 0),
                                                          itemBuilder:
                                                              (context, index) {
                                                            if (controller
                                                                    .isMe
                                                                    .value &&
                                                                index == 0) {
                                                              return Obx(() => CreatePostBtn(
                                                                  _showPostModal,
                                                                  isPostCreating: controller.isPostCreating.value));
                                                            } else {
                                                              final postIndex =
                                                                  controller
                                                                          .isMe
                                                                          .value
                                                                      ? index -
                                                                          1
                                                                      : index;
                                                              return PostCard(
                                                                post: controller.posts[
                                                                    postIndex],
                                                                imageUrl: MediaLinksHelper()
                                                                    .getPostReel(
                                                                        controller.posts[
                                                                            postIndex]),
                                                              );
                                                            }
                                                          },
                                                        ),
                                                      ],
                                                    ),
                                            ),
                                            ListView.builder(
                                              physics:
                                                  const AlwaysScrollableScrollPhysics(),
                                              padding:
                                                  const EdgeInsets.symmetric(
                                                      vertical: 8),
                                              itemCount: controller
                                                  .followers.length,
                                              itemBuilder: (context, index) {
                                                return LinearCard(
                                                  userName: controller
                                                      .followers[index]
                                                      .username,
                                                  imageUrl: controller
                                                      .followers[index]
                                                      .imageUrl,
                                                );
                                              },
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  );
                                },
                              ),
                            ),
                          ),
                        ],
                      );
                    }),
                  ),
                ),
              ),
            ));
  }
}
