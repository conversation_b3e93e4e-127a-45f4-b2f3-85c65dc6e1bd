import 'package:darve/api/models/user_model.dart';
import 'package:darve/components/RecordingsPage/RecordingIcon.dart';
import 'package:darve/ui/auth/sigin/sign_in_controller.dart';
import 'package:darve/pages/RecordingsPages/RecordingsPage.dart';
import 'package:darve/pages/ReelsPage.dart';
import 'package:darve/pages/profile_page_wrapper.dart';
import 'package:darve/pages/ChatPages/chat_list_page.dart';
import 'package:darve/pages/WalletScreen.dart';
import 'package:darve/providers/auth_provider.dart';
import 'package:darve/services/auth/auth_service.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ScreenHandler extends StatefulWidget {
  const ScreenHandler({super.key});

  @override
  State<ScreenHandler> createState() => _ScreenHandlerState();
}

class _ScreenHandlerState extends State<ScreenHandler> {
  UserModel? get userStore => authService.user;
  SignInController auth = Get.find<SignInController>();
  late AuthService authService;

  @override
  void initState() {
    super.initState();
    authService = AuthProvider.auth;
  }

  int selectedPageIndex = 0;

  @override
  Widget build(BuildContext context) {
    void resetSelectedPage() {
      setState(() {
        selectedPageIndex = 0;
      });
    }

    Widget getSelectedPage(int _pageIdx) {
      // Check if user data is available
      if (userStore == null) {
        // If no user data is available, logout and let middleware handle redirect
        authService.logout();
        return const Center(child: CircularProgressIndicator());
      }

      if (_pageIdx == 0) {
        return const ReelsPage();
      } else if (_pageIdx == 1) {
        return ChatListPage();
      } else if (_pageIdx == 2) {
        return const WalletScreen();
      } else if (_pageIdx == 3) {
        // Use new auth service user data if available, otherwise fallback to legacy
        final userId = userStore!.id;
        final username = userStore!.username;
        final imageUri = userStore!.imageUri!;
        final fullName = userStore!.name!;

        return ProfilePageWrapper(
          userId: userId,
          username: username,
          imageUrl: imageUri,
          fullName: fullName,
          goBackToReels: resetSelectedPage,
        );
      } else {
        return const RecordingsPage();
      }
    }

    return Scaffold(
            floatingActionButtonLocation:
                FloatingActionButtonLocation.centerDocked,
            floatingActionButton: FloatingActionButton(
              onPressed: () {
                setState(() {
                  selectedPageIndex = 4;
                });
              },
              shape: const CircleBorder(),
              backgroundColor: Styles.primaryColor,
              tooltip: "Create Challenge",
              child: selectedPageIndex == 4
                  ? const RecordingIcon()
                  : const Icon(
                      Icons.add,
                      color: Colors.white,
                      size: 35,
                    ),
            ),
            bottomNavigationBar: Container(
              decoration: BoxDecoration(
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withValues(alpha: .4),
                    spreadRadius: 1,
                    blurRadius: 8,
                    offset: const Offset(0, -2),
                  ),
                ],
              ),
              child: BottomAppBar(
                shape: const CircularNotchedRectangle(),
                notchMargin: 8.0,
                color: Colors.white,
                elevation: 0,
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 20.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      _buildNavItem(
                          Icon(
                            Icons.home_outlined,
                            color: (selectedPageIndex == 0)
                                ? Styles.primaryColor
                                : Colors.grey,
                          ),
                          'Home',
                          0),
                      _buildNavItem(
                          Image.asset(
                            'assets/images/navigation/chat.png',
                            width: 22,
                            height: 22,
                            color: (selectedPageIndex == 1)
                                ? Styles.primaryColor
                                : Colors.grey,
                          ),
                          'Inbox',
                          1),
                      const SizedBox(width: 40),
                      _buildNavItem(
                          Icon(
                            Icons.wallet,
                            color: (selectedPageIndex == 2)
                                ? Styles.primaryColor
                                : Colors.grey,
                          ),
                          'Wallet',
                          2),
                      _buildNavItem(
                          Icon(
                            Icons.person_outline,
                            color: (selectedPageIndex == 3)
                                ? Styles.primaryColor
                                : Colors.grey,
                          ),
                          'Profile',
                          3),
                    ],
                  ),
                ),
              ),
            ),
            body: getSelectedPage(selectedPageIndex),
          );
  }

  Widget _buildNavItem(Widget icon, String label, int index) {
    final isSelected = selectedPageIndex == index;
    return InkWell(
      onTap: () => setState(() => selectedPageIndex = index),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          icon,
          Text(
            label,
            style: TextStyle(
              color: isSelected ? Styles.primaryColor : Colors.grey,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }
}
