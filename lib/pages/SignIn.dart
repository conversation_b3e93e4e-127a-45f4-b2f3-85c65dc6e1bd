import 'package:darve/controllers/auth_controller.dart';
import 'package:darve/routes/route_helper.dart';
import 'package:darve/utils/errors.dart';
import 'package:darve/utils/styles.dart';
import 'package:darve/utils/validators.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SignInPage extends GetView<AuthController> {
  SignInPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          Container(
            color: Styles.primaryColor,
            height: double.infinity,
            width: double.infinity,
          ),
          Align(
            alignment: Alignment.topCenter,
            child: Padding(
              padding: const EdgeInsets.only(top: 20),
              child: SizedBox(
                width: 220,
                child: Image.asset(
                  "assets/images/darve-no-bg.png",
                  fit: BoxFit.fitWidth,
                ),
              ),
            ),
          ),
          Obx(() {
            // Listen to auth controller state changes
            if (controller.error != null) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _handleAuthError(context);
              });
            }

            // Navigate to home when successfully logged in
            if (controller.isLoggedIn.value) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                RouteHelper.goToHome();
              });
            }

            return AnimatedPositioned(
              duration: const Duration(milliseconds: 800),
              curve: Curves.easeOut,
              bottom: controller.isContainerVisible.value
                  ? 0
                  : -MediaQuery.of(context).size.height * 0.75,
              left: 0,
              right: 0,
              child: Container(
                decoration: const BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(50.0),
                    topRight: Radius.circular(50.0),
                  ),
                  color: Styles.foregroundColor,
                ),
                height: MediaQuery.of(context).size.height * 0.75,
                child: Column(
                  children: [
                    const Padding(
                      padding: EdgeInsets.only(top: 32.0, bottom: 10.0),
                      child: Text(
                        "Login",
                        style: TextStyle(
                          fontSize: 22.0,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                    const Text(
                      "Please login to continue...",
                      style: TextStyle(
                        color: Styles.textLightColor,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        top: 16.0,
                        left: 24.0,
                        right: 24.0,
                      ),
                      child: Form(
                        key: controller.formKey,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                          const Padding(
                            padding: EdgeInsets.only(left: 14, bottom: 4.0),
                            child: Text(
                              "Email",
                              style: TextStyle(
                                color: Styles.textLightColor,
                                fontSize: 14.0,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ),
                          Obx(() => TextFormField(
                            controller: controller.username,
                            validator: Validators.validateEmailOrUsername,
                            enabled: !controller.isSigningIn.value,
                            decoration: InputDecoration(
                              focusedBorder: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(16.0),
                                borderSide: const BorderSide(
                                  color:
                                      Styles.primaryColor, // Your desired color
                                  width: 1.0,
                                ),
                              ),
                              filled: true,
                              hintText: "Username",
                              hintStyle: const TextStyle(
                                color: Styles.textLightColor,
                                fontSize: 15.0,
                              ),
                              fillColor: Colors.white,
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(16.0),
                                borderSide: BorderSide.none,
                              ),
                              prefixIcon: const SizedBox(
                                width: 16,
                                height: 16,
                                child: Center(
                                  child: Icon(
                                    Icons.person_outline,
                                    color: Styles.primaryColor,
                                  ),
                                ),
                              ),
                              contentPadding: const EdgeInsets.symmetric(
                                vertical: 15.0,
                                horizontal: 20.0,
                              ),
                            ),
                            style: const TextStyle(fontWeight: FontWeight.w400),
                          )),
                          const SizedBox(height: 10.0),
                          const Padding(
                            padding: EdgeInsets.only(left: 14, bottom: 4.0),
                            child: Text(
                              "Password",
                              style: TextStyle(
                                color: Styles.textLightColor,
                                fontSize: 14.0,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                          ),
                          Obx(() {
                            return TextFormField(
                              controller: controller.password,
                              validator: Validators.validatePassword,
                              enabled: !controller.isSigningIn.value,
                              obscureText: !controller.isPasswordVisible.value,
                              obscuringCharacter: '*',
                            decoration: InputDecoration(
                                focusedBorder: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(16.0),
                                  borderSide: const BorderSide(
                                    color: Styles
                                        .primaryColor, // Your desired color
                                    width: 1.0,
                                  ),
                                ),
                                filled: true,
                                hintText: "********",
                                hintStyle: const TextStyle(
                                  color: Styles.textLightColor,
                                  fontSize: 16.0,
                                ),
                                fillColor: Colors.white,
                                border: OutlineInputBorder(
                                  borderRadius: BorderRadius.circular(16.0),
                                  borderSide: BorderSide.none,
                                ),
                                prefixIcon: SizedBox(
                                  width: 18,
                                  height: 16,
                                  child: Center(
                                    child: Image.asset(
                                      'assets/images/sign_in/lock.png',
                                      color: Styles.primaryColor,
                                      fit: BoxFit.contain,
                                      width: 16,
                                      height: 18,
                                    ),
                                  ),
                                ),
                                suffixIcon: IconButton(
                                  icon: Icon(
                                    controller.isPasswordVisible.value
                                        ? Icons.visibility_outlined
                                        : Icons.visibility_off_outlined,
                                    color: Styles.primaryColor,
                                    size: 16,
                                  ),
                                  onPressed: () {
                                    controller.isPasswordVisible.value =
                                        !controller.isPasswordVisible.value;
                                  },
                                ),
                                contentPadding: const EdgeInsets.symmetric(
                                  vertical: 15.0,
                                  horizontal: 20.0,
                                ),
                              ),
                              style: const TextStyle(
                                fontWeight: FontWeight.w400,
                              ),
                            );
                          }),
                          const SizedBox(height: 18.0),
                          GestureDetector(
                            onTap: () {
                              RouteHelper.goToForgotPassword();
                            },
                            child: const Center(
                              child: Text(
                                "Forgot Password?",
                                style: TextStyle(
                                  color: Styles.textLightColor,
                                  fontWeight: FontWeight.w400,
                                  fontSize: 14,
                                ),
                              ),
                            ),
                          ),
                          const SizedBox(height: 34.0),
                          Center(
                            child: SizedBox(
                              width: MediaQuery.of(context).size.width * 0.675,
                              child: Obx(() => ElevatedButton(
                                onPressed: controller.isSigningIn.value ? null : controller.signIn,
                                child: controller.isSigningIn.value
                                    ? const SizedBox(
                                        height: 20,
                                        width: 20,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                                        ),
                                      )
                                    : const Text("Log in"),
                              )),
                            ),
                          ),
                          const SizedBox(height: 40.0),
                          const Row(
                            children: <Widget>[
                              Expanded(
                                child: Divider(
                                  color: Styles.primaryColor,
                                  thickness: 1.74,
                                ),
                              ),
                              Padding(
                                padding: EdgeInsets.symmetric(horizontal: 12.0),
                                child: Text(
                                  "OR",
                                  style: TextStyle(
                                    color: Styles.primaryColor,
                                    fontSize: 12.0,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: Divider(
                                  color: Styles.primaryColor,
                                  thickness: 1.74,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 22.0),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              IconButton(
                                onPressed: () {
                                  controller.signInWithGoogle();
                                },
                                icon: buildIconContainerFromAsset(
                                  'assets/images/sign_in/google-fill-w.png',
                                ),
                              ),
                              const SizedBox(width: 5.0),
                              IconButton(
                                onPressed: () {
                                  controller.signInWithFacebook();
                                },
                                icon: buildIconContainerFromAsset(
                                  'assets/images/sign_in/facebook.png', size: 50
                                ),
                              ),
                              const SizedBox(width: 5.0),
                              IconButton(
                                onPressed: () {
                                  controller.signInWithApple();
                                },
                                icon: buildIconContainer(Icons.apple),
                              ),
                            ],
                          ),
                          const SizedBox(height: 35.0),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Text(
                                "New User? ",
                                style: TextStyle(
                                  color: Styles.textLightColor,
                                  fontWeight: FontWeight.w400,
                                  fontSize: 14,
                                ),
                              ),
                              GestureDetector(
                                onTap: () {
                                  RouteHelper.goToSignUp();
                                },
                                child: const Text(
                                  "Sign up",
                                  style: TextStyle(
                                    color: Styles.primaryColor,
                                    fontWeight: FontWeight.w400,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          }),
        ],
      ),
    );
  }

  Widget buildIconContainer(IconData icon) {
    return CircleAvatar(
      radius: 25,
      backgroundColor: Styles.primaryColor,
      child: Icon(icon, color: Colors.white, size: 32),
    );
  }

  Widget buildIconContainerFromAsset(String assetPath, {double size = 32}) {
    return CircleAvatar(
      radius: 25,
      backgroundColor: Styles.primaryColor,
      child: Image.asset(assetPath, width: size, height: size),
    );
  }

  void _handleAuthError(BuildContext context) {
    if (controller.error != null) {
      ErrorsHandle().displayErrorToast(controller.error);
      controller.clearError();
    }
  }
}
