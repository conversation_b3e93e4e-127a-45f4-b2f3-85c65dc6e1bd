import 'package:darve/utils/interfaces/ShortenedComment.dart';
import 'package:darve/utils/serverAssets.dart';
import 'package:flutter/material.dart';

class CommentTile extends StatelessWidget {
  final ShortenedComment comment;
  const CommentTile(this.comment, {super.key});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: CircleAvatar(
        backgroundImage: NetworkImage(
          ServerAssets().getAssetUrl(comment.avatar!),
        ),
        radius: 20, // Adjust the radius as needed
      ),
      // title: Text(comment["username"]!),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                comment.username!,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
              const Spacer(),
              Text(comment.likes!,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w400,
                  )),
              const SizedBox(width: 8),
              InkWell(
                onTap: () {
                  // Handle username tap (e.g., navigate to profile)
                },
                child: const Icon(Icons.favorite_border, size: 16),
              ),
            ],
          ),
          Text(
            comment.comment!,
            style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: Colors.black54),
          ),
          const SizedBox(height: 4),
          const Text(
            "Reply",
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
        ],
      ),
    );
  }
}
