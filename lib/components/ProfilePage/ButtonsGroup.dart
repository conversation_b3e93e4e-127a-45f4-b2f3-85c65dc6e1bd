import 'package:darve/components/common/showSnackbar.dart';
import 'package:darve/controllers/profile_controller.dart';
import 'package:darve/utils/styles.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ProfileButtonsGroup extends StatefulWidget {
  final String userId;
  final String username;
  final String imageUrl;
  final ProfileController profileController;
  final Function() createChatWithUserId;

  const ProfileButtonsGroup({
    super.key,
    required this.userId,
    required this.username,
    required this.imageUrl,
    required this.profileController,
    required this.createChatWithUserId,
  });

  @override
  State<ProfileButtonsGroup> createState() => _ProfileButtonsGroupState();
}

class _ProfileButtonsGroupState extends State<ProfileButtonsGroup> {

  @override
  Widget build(BuildContext context) {
    return Obx(() => Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        OutlinedButton(
          onPressed: widget.profileController.isFollowLoading.value
              ? null
              : () async {
                  await widget.profileController.toggleFollow();

                  // Show snackbar feedback
                  SnackbarHelper.showFollowSnackbar(
                    context: context,
                    imageUri: widget.imageUrl,
                    username: widget.username,
                    bgColor: widget.profileController.isFollowing.value
                        ? Colors.green
                        : Colors.red,
                    isFollowed: widget.profileController.isFollowing.value,
                  );
                },
          style: OutlinedButton.styleFrom(
            side: const BorderSide(color: Colors.black),
            padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(30),
            ),
          ),
          child: widget.profileController.isFollowLoading.value
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.black),
                  ),
                )
              : Text(
                  widget.profileController.isFollowing.value ? 'Unfollow' : 'Follow',
                  style: const TextStyle(
                    color: Colors.black,
                    fontSize: 16,
                  ),
                ),
        ),
        const SizedBox(width: 16),
        ElevatedButton(
          onPressed: widget.profileController.isChatCreating.value
              ? null
              : widget.createChatWithUserId,
          style: ElevatedButton.styleFrom(
            backgroundColor: Styles.primaryColor,
            padding: const EdgeInsets.symmetric(horizontal: 50, vertical: 6),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(30),
            ),
          ),
          child: widget.profileController.isChatCreating.value
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : const Text(
                  'Message',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
        ),
      ],
    ));
  }
}
