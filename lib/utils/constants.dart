class DarveConstants {
  // final String backendUrl = "http://*************:8080";
  // final String backendUrl = "https://darve.reefscan.info";
  // final String backendUrl = "http://localhost:8080";
}

enum WalletType { deposit, withdraw, transfer }

// Extension to map enum values to strings
extension WalletTypeExtension on WalletType {
  String get value {
    switch (this) {
      case WalletType.deposit:
        return 'Deposit';
      case WalletType.withdraw:
        return 'Withdraw';
      case WalletType.transfer:
        return 'Transfer';
    }
  }
}

enum ChallengeType { all, requested, accepted, rejected, delivered }

extension ChallengeTypeExtension on ChallengeType {
  String get value {
    switch (this) {
      case ChallengeType.all:
        return 'All';
      case ChallengeType.requested:
        return 'Requested';
      case ChallengeType.accepted:
        return 'Accepted';
      case ChallengeType.rejected:
        return 'Rejected';
      case ChallengeType.delivered:
        return 'Delivered';
    }
  }
}

enum RequestCacheType { history, chatList, discussion }

extension RequestCacheTypeExtension on RequestCacheType {
  String get value {
    switch (this) {
      case RequestCacheType.history:
        return 'history_cache';
      case RequestCacheType.chatList:
        return 'chat_list_cache';
      case RequestCacheType.discussion:
        return 'chat_discussion_cache';
    }
  }
}

/// Centralized API endpoint paths for server requests
class ApiPaths {
  // Auth
  static const String login = '/api/login';
  static const String register = '/api/register';
  static const String forgotPassword = '/api/forgot_password';
  static const String resetPassword = '/api/reset_password';
  static const String signWithFacebook = '/api/auth/sign_with_facebook';
  static const String signWithApple = '/api/auth/sign_with_apple';
  static const String signWithGoogle = '/api/auth/sign_with_google';

  // User
  static const String setPassword = '/api/login'; // Used in UserRepository
  static const String changePassword = '/api/login'; // Used in UserRepository
  static const String emailVerificationStart = '/api/users/current/email/verification/start';
  static const String emailVerificationConfirm = '/api/users/current/email/verification/confirm';

  // Profile
  static String userProfile(String username) => '/u/$username';
  static String followUser(String userId) => '/api/follow/$userId';
  static String unfollowUser(String userId) => '/api/unfollow/$userId';
  static const String searchUser = '/api/user/search';
  static String getFollowers(String userId) => '/api/user/$userId/followers';
  static String isFollowing(String userId) => '/api/user/$userId/is-following';
  static const String editProfile = '/api/user/profile/edit';
  static String getFollowing(String userId) => '/api/user/$userId/following';

  // Posts
  static String getPosts(String username) => '/api/user/$username/posts';
  static const String getFollowingPosts = '/u/following/posts';
  static const String createPost = '/api/user/post';
  static String getPostDiscussions(String discussionId, String postId) => '/api/discussion/$discussionId/post/$postId';
  static String postInDiscussion(String discussionId, String postUri) => '/api/discussion/$discussionId/post/$postUri/reply';
  static String likePost(String postId) => '/api/post/$postId/like';
  static String unlikePost(String postId) => likePost(postId);
  static String sharePost(String postId) => '/api/post/$postId/share';
  static String deletePost(String postId) => '/api/post/$postId';
  static String editPost(String postId) => '/api/post/$postId';
  static String getPostComments(String postId) => '/api/post/$postId/comments';
  static String addComment(String postId) => '/api/post/$postId/comment';

  // Chat
  static const String getChatsList = '/api/user_chat/list';
  static String createChatWithUserId(String otherUserId) => '/api/user_chat/with/$otherUserId';
  static String postMessageInChat(String discussionId) => '/api/discussion/$discussionId/post';
  static String getChatMessages(String chatId) => '/api/chat/$chatId/messages';
  static String markChatAsRead(String chatId) => '/api/chat/$chatId/read';
  static String deleteChatMessage(String messageId) => '/api/chat/message/$messageId';
  static String editChatMessage(String messageId) => '/api/chat/message/$messageId';
  static String getChatDiscussionById(String discussionId) => '/api/discussion/$discussionId';
  static const String getChatListSse = '/api/events/sse';
  static String getChatDiscussionByIdSse(String discussionId) => '/api/discussion/$discussionId/sse';

  // Challenge
  static String getTaskRequests(String postId) => '/api/task_request/list/post/$postId';
  static const String getAllReceivedTaskRequests = '/api/task_request/received';
  static String getReceivedTaskRequestsForPost(String postId) => '/api/task_request/received/post/$postId';
  static String acceptChallenge(String taskId) => '/api/task_request/$taskId/accept';
  static String deliverTaskRequest(String taskId) => '/api/task_request/$taskId/deliver';
  static const String createChallenge = '/api/challenge';
  static const String getMyChallenges = '/api/challenges/my';
  static const String getGivenChallenges = '/api/challenges/given';
  static String addParticipant(String challengeId) => '/api/challenge/$challengeId/participant';
  static const String createTaskRequest = '/api/task_request';
  static String getGivenTaskRequestsForPost(String postId) => '/api/task_request/given/post/$postId';

  // Wallet
  static const String getBalance = '/api/user/wallet/balance';
  static const String getUserTxHistory = '/api/user/wallet/history';
  static String createPaymentIntent(String amount) => '/api/user/wallet/endowment/$amount';
  static const String getBalanceSse = '/api/user/wallet/balance/sse';
  static const String transferFunds = '/api/user/wallet/transfer';
  static const String withdrawFunds = '/api/user/wallet/withdraw';

  // Notifications
  static const String getNotifications = '/api/notifications';
  static String readNotification(String id) => '/api/notifications/$id/read';
  static const String readAllNotifications = '/api/notifications/read';
  static const String getCountOfUnreadNotifications = '/api/notifications/count';
  static const String notificationsSse = '/api/notifications/sse';
}
